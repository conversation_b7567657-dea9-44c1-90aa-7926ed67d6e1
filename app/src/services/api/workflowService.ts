/**
 * Service for handling workflow record approval operations
 * Integrated with backend workflow API endpoints
 */

import { apiClient } from './client';
import { 
  RecordApprovalResponse, 
  ApprovalActionRequest 
} from '@/types/approvalWorkflow.types';
import { ApiResponse } from '@/types/api/common';

export class WorkflowService {
  private static readonly BASE_URL = '/api/v1/organizations';

  /**
   * Get pending approvals for a user
   * @param organizationId Organization ID
   * @param userId User ID
   * @returns Promise with list of pending record approvals
   */
  static async getPendingApprovals(
    organizationId: number,
    userId: number
  ): Promise<RecordApprovalResponse[]> {
    try {
      const response = await apiClient.get<RecordApprovalResponse[]>(
        `${this.BASE_URL}/${organizationId}/workflows/approvals/pending/user/${userId}`
      );
      return response;
    } catch (error) {
      console.error('Error fetching pending approvals:', error);
      throw error;
    }
  }

  /**
   * Approve a record in the workflow
   * @param organizationId Organization ID
   * @param recordApprovalId Record approval ID
   * @param request Approval action request with user ID and optional comment
   * @returns Promise with updated record approval response
   */
  static async approveRecord(
    organizationId: number,
    recordApprovalId: number,
    request: ApprovalActionRequest
  ): Promise<ApiResponse<RecordApprovalResponse>> {
    try {
      const response = await apiClient.put<RecordApprovalResponse>(
        `${this.BASE_URL}/${organizationId}/workflows/approvals/${recordApprovalId}/approve`,
        request
      );

      return {
        data: response,
        message: 'Record approved successfully',
        status: 200
      };
    } catch (error) {
      console.error('Error approving record:', error);
      throw error;
    }
  }

  /**
   * Reject a record in the workflow
   * @param organizationId Organization ID
   * @param recordApprovalId Record approval ID
   * @param request Approval action request with user ID and optional comment
   * @returns Promise with updated record approval response
   */
  static async rejectRecord(
    organizationId: number,
    recordApprovalId: number,
    request: ApprovalActionRequest
  ): Promise<ApiResponse<RecordApprovalResponse>> {
    try {
      const response = await apiClient.put<RecordApprovalResponse>(
        `${this.BASE_URL}/${organizationId}/workflows/approvals/${recordApprovalId}/reject`,
        request
      );

      return {
        data: response,
        message: 'Record rejected successfully',
        status: 200
      };
    } catch (error) {
      console.error('Error rejecting record:', error);
      throw error;
    }
  }

  /**
   * Get initiated approvals for a user
   * @param organizationId Organization ID
   * @param userId User ID
   * @returns Promise with list of initiated record approvals
   */
  static async getInitiatedApprovals(
    organizationId: number,
    userId: number
  ): Promise<RecordApprovalResponse[]> {
    try {
      const response = await apiClient.get<RecordApprovalResponse[]>(
        `${this.BASE_URL}/${organizationId}/workflows/approvals/initiated/user/${userId}`
      );
      return response;
    } catch (error) {
      console.error('Error fetching initiated approvals:', error);
      throw error;
    }
  }
}
