import Button from '@/components/ui/Button/Button';
import { ColumnConfig, DataGrid } from '@/components/ui/DataGrid/DataGrid';
import Modal from '@/components/ui/Modal/Modal';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';
import Typography from '@/components/ui/Typography';
import { useOrganizationContext } from '@/context/OrganizationContext';
import { useAuth } from '@/hooks/useAuth';
import { usePurchaseRequests, usePurchaseRequestsByUser } from '@/hooks/usePurchaseRequest';
import { usePendingApprovals, useApproveRecord, useRejectRecord } from '@/hooks/useWorkflow';
import { getCategoryById, getOrganizationProductById, getProductById } from '@/services/api/catalogService';
import { UserService } from '@/services/api/userService';
import { PurchaseRequest, PurchaseRequestItem } from '@/types/catalog.types';
import { RecordApprovalResponse } from '@/types/approvalWorkflow.types';
import { formatDate } from '@/utils/dateUtils';
import { useNavigate } from '@tanstack/react-router';
import { TabPanel, TabView } from 'primereact/tabview';
import React, { useEffect, useRef, useState } from 'react';
import './PurchaseRequestManagement.css';
import { createPurchaseRequestRoute } from '@/routes/private/createPurchaseRequest.route';

const PurchaseRequestManagement: React.FC = () => {
  const navigate = useNavigate();
  const toast = useRef<ToastRef>(null);
  const { organizationId } = useOrganizationContext();
  const { user } = useAuth();

  // State for active tab
  const [activeIndex, setActiveIndex] = useState(0);

  // State for products modal
  const [isProductsModalOpen, setIsProductsModalOpen] = useState(false);
  const [selectedPurchaseRequest, setSelectedPurchaseRequest] = useState<PurchaseRequest | null>(null);

  // State for approval confirmation modals
  const [isApproveModalOpen, setIsApproveModalOpen] = useState(false);
  const [isRejectModalOpen, setIsRejectModalOpen] = useState(false);
  const [selectedRecordApproval, setSelectedRecordApproval] = useState<RecordApprovalResponse | null>(null);

  // State for user name cache
  const [userNameCache, setUserNameCache] = useState<Map<number, string>>(new Map());

  // State for category name cache
  const [categoryNameCache, setCategoryNameCache] = useState<Map<number, string>>(new Map());

  // State for product name cache
  const [productNameCache, setProductNameCache] = useState<Map<string, string>>(new Map());

  // State for purchase request cache for My Actions tab
  const [purchaseRequestCache, setPurchaseRequestCache] = useState<Map<number, PurchaseRequest>>(new Map());

  // Fetch all purchase requests
  const { data: purchaseRequestsData, isLoading: isPurchaseRequestsLoading } = usePurchaseRequests(
    organizationId || 0,
    { sort: 'requestDate,desc' }
  );
  const purchaseRequests = purchaseRequestsData?.data || [];

  // Fetch user's purchase requests
  const { data: userPurchaseRequestsData, isLoading: isUserPurchaseRequestsLoading } = usePurchaseRequestsByUser(
    organizationId || 0,
    user?.id || 0
  );
  const userPurchaseRequests = userPurchaseRequestsData || [];

  // Fetch pending approvals for My Actions tab
  const { data: pendingApprovalsData, isLoading: isPendingApprovalsLoading } = usePendingApprovals(
    organizationId || 0,
    user?.id || 0
  );
  const pendingApprovals = pendingApprovalsData || [];

  // Mutations for approve/reject actions
  const approveRecordMutation = useApproveRecord();
  const rejectRecordMutation = useRejectRecord();

  // Helper function to fetch user name
  const fetchUserName = async (userId: number): Promise<string> => {
    if (userNameCache.has(userId)) {
      return userNameCache.get(userId)!;
    }

    try {
      const user = await UserService.getUserById(userId, organizationId!);
      const name = user.name || 'Unknown User';
      setUserNameCache(prev => new Map(prev.set(userId, name)));
      return name;
    } catch (error) {
      const fallbackName = 'Unknown User';
      setUserNameCache(prev => new Map(prev.set(userId, fallbackName)));
      return fallbackName;
    }
  };

  // Helper function to fetch category name
  const fetchCategoryName = async (categoryId: number): Promise<string> => {
    if (categoryNameCache.has(categoryId)) {
      return categoryNameCache.get(categoryId)!;
    }

    try {
      const categoryResponse = await getCategoryById(categoryId);
      const name = categoryResponse.data?.name || 'Unknown Category';
      setCategoryNameCache(prev => new Map(prev.set(categoryId, name)));
      return name;
    } catch (error) {
      const fallbackName = 'Unknown Category';
      setCategoryNameCache(prev => new Map(prev.set(categoryId, fallbackName)));
      return fallbackName;
    }
  };

  // Helper function to fetch product name
  const fetchProductName = async (productId: number, organizationProductId: number | undefined): Promise<string> => {
    const cacheKey = organizationProductId ? `org_${organizationProductId}` : `product_${productId}`;

    if (productNameCache.has(cacheKey)) {
      return productNameCache.get(cacheKey)!;
    }

    try {
      let name = 'Unknown Product';

      if (organizationProductId) {
        const orgProductResponse = await getOrganizationProductById(organizationId!, organizationProductId);
        name = orgProductResponse.data?.name || 'Unknown Organization Product';
      } else if (productId) {
        const productResponse = await getProductById(productId);
        name = productResponse.data?.name || 'Unknown Product';
      }

      setProductNameCache(prev => new Map(prev.set(cacheKey, name)));
      return name;
    } catch (error) {
      const fallbackName = organizationProductId ? 'Unknown Organization Product' : 'Unknown Product';
      setProductNameCache(prev => new Map(prev.set(cacheKey, fallbackName)));
      return fallbackName;
    }
  };

  // Component for displaying user name
  const UserNameCell: React.FC<{ userId: number }> = ({ userId }) => {
    const [name, setName] = useState<string>('Loading...');

    useEffect(() => {
      fetchUserName(userId).then(setName);
    }, [userId]);

    return <div className="user-name-cell">{name}</div>;
  };

  // Component for displaying category name
  const CategoryNameCell: React.FC<{ categoryId: number }> = ({ categoryId }) => {
    const [name, setName] = useState<string>('Loading...');

    useEffect(() => {
      fetchCategoryName(categoryId).then(setName);
    }, [categoryId]);

    return <div className="category-name-cell">{name}</div>;
  };

  // Component for displaying product name
  const ProductNameCell: React.FC<{ item: PurchaseRequestItem }> = ({ item }) => {
    const [name, setName] = useState<string>('Loading...');
    const [productType, setProductType] = useState<string>('');

    useEffect(() => {
      if (item.productId || item.organizationProductId) {
        fetchProductName(item.productId || 0, item.organizationProductId).then(setName);
        setProductType(item.organizationProductId ? 'Organization Product' : 'Global Product');
      } else {
        setName('Unknown Product');
        setProductType('Unknown Type');
      }
    }, [item.productId, item.organizationProductId]);

    return (
      <div className="product-name-cell">
        <div className="font-medium">{name}</div>
        <div className="text-sm text-color-secondary">{productType}</div>
      </div>
    );
  };

  // Helper function to fetch purchase request by ID
  const fetchPurchaseRequestById = async (recordId: number): Promise<PurchaseRequest | null> => {
    if (purchaseRequestCache.has(recordId)) {
      return purchaseRequestCache.get(recordId)!;
    }

    try {
      // First try to find in existing data
      const existingRequest = purchaseRequests.find(pr => pr.id === recordId) ||
                             userPurchaseRequests.find(pr => pr.id === recordId);

      if (existingRequest) {
        setPurchaseRequestCache(prev => new Map(prev.set(recordId, existingRequest)));
        return existingRequest;
      }

      // If not found in existing data, we'll return null for now
      // In a real implementation, you might want to fetch from API
      return null;
    } catch (error) {
      console.error('Error fetching purchase request:', error);
      return null;
    }
  };

  // Handle approve action
  const handleApprove = (recordApproval: RecordApprovalResponse) => {
    setSelectedRecordApproval(recordApproval);
    setIsApproveModalOpen(true);
  };

  // Handle reject action
  const handleReject = (recordApproval: RecordApprovalResponse) => {
    setSelectedRecordApproval(recordApproval);
    setIsRejectModalOpen(true);
  };

  // Confirm approve action
  const confirmApprove = async () => {
    if (!selectedRecordApproval || !user?.id) return;

    try {
      await approveRecordMutation.mutateAsync({
        organizationId: organizationId!,
        recordApprovalId: selectedRecordApproval.id,
        request: {
          userId: user.id,
          comment: ''
        }
      });

      toast.current?.showSuccess('Record approved successfully');

      setIsApproveModalOpen(false);
      setSelectedRecordApproval(null);
    } catch (error) {
      toast.current?.showError('Failed to approve record');
    }
  };

  // Confirm reject action
  const confirmReject = async () => {
    if (!selectedRecordApproval || !user?.id) return;

    try {
      await rejectRecordMutation.mutateAsync({
        organizationId: organizationId!,
        recordApprovalId: selectedRecordApproval.id,
        request: {
          userId: user.id,
          comment: ''
        }
      });

      toast.current?.showSuccess('Record rejected successfully');

      setIsRejectModalOpen(false);
      setSelectedRecordApproval(null);
    } catch (error) {
      toast.current?.showError('Failed to reject record');
    }
  };

  // Handle view products
  const handleViewProducts = (purchaseRequest: PurchaseRequest) => {
    setSelectedPurchaseRequest(purchaseRequest);
    setIsProductsModalOpen(true);
  };

  // Define columns for the purchase requests table
  const columns: ColumnConfig[] = [
    {
      field: 'requestedById',
      header: 'Requested By',
      sortable: true,
      body: (rowData: PurchaseRequest) => (
        <UserNameCell userId={rowData.requestedById} />
      ),
    },
    {
      field: 'requestStatus',
      header: 'Request Status',
      sortable: true,
      body: (rowData: PurchaseRequest) => (
        <span className={`status-badge status-${rowData.requestStatus?.toLowerCase()}`}>
          {rowData.requestStatus || 'PENDING'}
        </span>
      ),
    },
    {
      field: 'requestType',
      header: 'Request Type',
      sortable: true,
    },
    {
      field: 'procurementSource',
      header: 'Procurement Source',
      sortable: true,
    },
    {
      field: 'productCategoryId',
      header: 'Product Category',
      sortable: true,
      body: (rowData: PurchaseRequest) => (
        <CategoryNameCell categoryId={rowData.productCategoryId} />
      ),
    },
    {
      field: 'expectedDeliveryDate',
      header: 'Expected Delivery Date',
      sortable: true,
      body: (rowData: PurchaseRequest) => formatDate(rowData.expectedDeliveryDate),
    },
    {
      field: 'actions',
      header: 'Products',
      body: (rowData: PurchaseRequest) => (
        <Button
          variant="outline"
          icon="pi pi-eye"
          size="small"
          onClick={() => handleViewProducts(rowData)}
        />
      ),
    },
  ];

  // Define columns for the user's purchase requests table (without "Requested By" column)
  const userColumnsConfig: ColumnConfig[] = [
    {
      field: 'requestStatus',
      header: 'Request Status',
      sortable: true,
      body: (rowData: PurchaseRequest) => (
        <span className={`status-badge status-${rowData.requestStatus?.toLowerCase()}`}>
          {rowData.requestStatus || 'PENDING'}
        </span>
      ),
    },
    {
      field: 'requestType',
      header: 'Request Type',
      sortable: true,
    },
    {
      field: 'procurementSource',
      header: 'Procurement Source',
      sortable: true,
    },
    {
      field: 'productCategoryId',
      header: 'Product Category',
      sortable: true,
      body: (rowData: PurchaseRequest) => (
        <CategoryNameCell categoryId={rowData.productCategoryId} />
      ),
    },
    {
      field: 'expectedDeliveryDate',
      header: 'Expected Delivery Date',
      sortable: true,
      body: (rowData: PurchaseRequest) => formatDate(rowData.expectedDeliveryDate),
    },
    {
      field: 'actions',
      header: 'Products',
      body: (rowData: PurchaseRequest) => (
        <Button
          variant="outline"
          icon="pi pi-eye"
          size="small"
          onClick={() => handleViewProducts(rowData)}
        />
      ),
    },
  ];

  // Prepare data for My Actions tab by combining record approvals with purchase requests
  const [myActionsData, setMyActionsData] = useState<any[]>([]);
  const [isMyActionsLoading, setIsMyActionsLoading] = useState(false);

  useEffect(() => {
    const prepareMyActionsData = async () => {
      if (!pendingApprovals.length) {
        setMyActionsData([]);
        return;
      }

      setIsMyActionsLoading(true);
      const combinedData = [];

      for (const recordApproval of pendingApprovals) {
        const purchaseRequest = await fetchPurchaseRequestById(recordApproval.recordId);
        combinedData.push({
          recordApproval,
          purchaseRequest,
          id: recordApproval.id // For DataGrid key
        });
      }

      setMyActionsData(combinedData);
      setIsMyActionsLoading(false);
    };

    prepareMyActionsData();
  }, [pendingApprovals, purchaseRequests, userPurchaseRequests]);

  // Define columns for the My Actions tab
  const myActionsColumnsConfig: ColumnConfig[] = [
    {
      field: 'requestedById',
      header: 'Requested By',
      sortable: true,
      body: (rowData: any) => {
        const purchaseRequest = rowData.purchaseRequest;
        return purchaseRequest ? (
          <UserNameCell userId={purchaseRequest.requestedById} />
        ) : (
          <div>Loading...</div>
        );
      },
    },
    {
      field: 'requestStatus',
      header: 'Status',
      sortable: true,
      body: (rowData: any) => {
        const purchaseRequest = rowData.purchaseRequest;
        return purchaseRequest ? (
          <span className={`status-badge status-${purchaseRequest.requestStatus?.toLowerCase()}`}>
            {purchaseRequest.requestStatus || 'PENDING'}
          </span>
        ) : (
          <div>Loading...</div>
        );
      },
    },
    {
      field: 'requestType',
      header: 'Type',
      sortable: true,
      body: (rowData: any) => {
        const purchaseRequest = rowData.purchaseRequest;
        return purchaseRequest ? purchaseRequest.requestType : 'Loading...';
      },
    },
    {
      field: 'procurementSource',
      header: 'Procurement Source',
      sortable: true,
      body: (rowData: any) => {
        const purchaseRequest = rowData.purchaseRequest;
        return purchaseRequest ? purchaseRequest.procurementSource : 'Loading...';
      },
    },
    {
      field: 'productCategoryId',
      header: 'Category',
      sortable: true,
      body: (rowData: any) => {
        const purchaseRequest = rowData.purchaseRequest;
        return purchaseRequest ? (
          <CategoryNameCell categoryId={purchaseRequest.productCategoryId} />
        ) : (
          <div>Loading...</div>
        );
      },
    },
    {
      field: 'expectedDeliveryDate',
      header: 'Expected Delivery Date',
      sortable: true,
      body: (rowData: any) => {
        const purchaseRequest = rowData.purchaseRequest;
        return purchaseRequest ? formatDate(purchaseRequest.expectedDeliveryDate) : 'Loading...';
      },
    },
    {
      field: 'actions',
      header: 'Actions',
      body: (rowData: any) => {
        const purchaseRequest = rowData.purchaseRequest;
        return (
          <div className="flex gap-2">
            <Button
              variant="outline"
              icon="pi pi-eye"
              size="small"
              onClick={() => purchaseRequest && handleViewProducts(purchaseRequest)}
              disabled={!purchaseRequest}
            />
            <Button
              variant="primary"
              icon="pi pi-check"
              size="small"
              onClick={() => handleApprove(rowData.recordApproval)}
            />
            <Button
              variant="danger"
              icon="pi pi-times"
              size="small"
              onClick={() => handleReject(rowData.recordApproval)}
            />
          </div>
        );
      },
    },
  ];

  return (
    <div className="purchase-request-management p-4">
      <Toast ref={toast} position="top-right" />
      <Typography variant="h5" weight="semibold" className="mb-4">
        Purchase Request Management
      </Typography>
      <div className="flex gap-2 mb-4">
        <Button variant="primary" size="small" onClick={() => navigate({ to: createPurchaseRequestRoute.to })}>
          Add Product
        </Button>
      </div>

      <TabView activeIndex={activeIndex} onTabChange={(e) => setActiveIndex(e.index)}>
        <TabPanel header="All Purchase Requests">
          <div className="tab-content">
            <DataGrid
              value={purchaseRequests}
              columns={columns}
              totalRecords={purchaseRequests.length}
              loading={isPurchaseRequestsLoading}
              rows={20}
              rowsPerPageOptions={[20, 50, 100]}
              showGridLines={true}
              stripedRows={true}
              emptyMessage="No purchase requests found"
            />
          </div>
        </TabPanel>
        
        <TabPanel header="My Purchase Requests">
          <div className="tab-content">
            <DataGrid
              value={userPurchaseRequests}
              columns={userColumnsConfig}
              totalRecords={userPurchaseRequests.length}
              loading={isUserPurchaseRequestsLoading}
              rows={20}
              rowsPerPageOptions={[20, 50, 100]}
              showGridLines={true}
              stripedRows={true}
              emptyMessage="No purchase requests found"
            />
          </div>
        </TabPanel>
        
        <TabPanel header="My Actions">
          <div className="tab-content">
            <DataGrid
              value={myActionsData}
              columns={myActionsColumnsConfig}
              totalRecords={myActionsData.length}
              loading={isMyActionsLoading || isPendingApprovalsLoading}
              rows={20}
              rowsPerPageOptions={[20, 50, 100]}
              showGridLines={true}
              stripedRows={true}
              emptyMessage="No pending approvals found"
            />
          </div>
        </TabPanel>
      </TabView>

      {/* Products Modal */}
      <Modal
        visible={isProductsModalOpen}
        onHide={() => setIsProductsModalOpen(false)}
        header="Purchase Request Products"
        modalProps={{ style: { width: '70vw' } }}
      >
        {selectedPurchaseRequest && (
          <div className="products-modal-content">
            <Typography variant="h6" className="mb-3">
              Products for Request #{selectedPurchaseRequest.id}
            </Typography>

            {selectedPurchaseRequest.purchaseRequestItems && selectedPurchaseRequest.purchaseRequestItems.length > 0 ? (
              <div className="products-list">
                {selectedPurchaseRequest.purchaseRequestItems.map((item: PurchaseRequestItem, index: number) => (
                  <div key={index} className="product-item p-3 mb-2 border-1 border-round">
                    <div className="flex justify-content-between align-items-center">
                      <div className="product-info">
                        <ProductNameCell item={item} />
                        {item.onlineUrl && (
                          <Typography variant="body2" className="text-color-secondary mt-1">
                            URL: {item.onlineUrl}
                          </Typography>
                        )}
                      </div>
                      <div className="quantity-info">
                        <Typography variant="body1" weight="medium">
                          Qty: {item.quantity}
                        </Typography>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <Typography variant="body1" className="text-center p-4">
                No products found for this request
              </Typography>
            )}
          </div>
        )}
      </Modal>

      {/* Approve Confirmation Modal */}
      <Modal
        visible={isApproveModalOpen}
        onHide={() => {
          setIsApproveModalOpen(false);
          setSelectedRecordApproval(null);
        }}
        header="Confirm Approval"
        footerButtons={[
          {
            label: "Cancel",
            onClick: () => {
              setIsApproveModalOpen(false);
              setSelectedRecordApproval(null);
            },
            variant: "outline"
          },
          {
            label: "Approve",
            onClick: confirmApprove,
            variant: "primary",
            isLoading: approveRecordMutation.isPending
          }
        ]}
      >
        <div className="confirmation-content">
          <i className="pi pi-check-circle text-green-500 text-2xl mr-3"></i>
          <span>
            Are you sure you want to approve this purchase request?
          </span>
        </div>
      </Modal>

      {/* Reject Confirmation Modal */}
      <Modal
        visible={isRejectModalOpen}
        onHide={() => {
          setIsRejectModalOpen(false);
          setSelectedRecordApproval(null);
        }}
        header="Confirm Rejection"
        footerButtons={[
          {
            label: "Cancel",
            onClick: () => {
              setIsRejectModalOpen(false);
              setSelectedRecordApproval(null);
            },
            variant: "outline"
          },
          {
            label: "Reject",
            onClick: confirmReject,
            variant: "danger",
            isLoading: rejectRecordMutation.isPending
          }
        ]}
      >
        <div className="confirmation-content">
          <i className="pi pi-times-circle text-red-500 text-2xl mr-3"></i>
          <span>
            Are you sure you want to reject this purchase request?
          </span>
        </div>
      </Modal>
    </div>
  );
};

export default PurchaseRequestManagement;
