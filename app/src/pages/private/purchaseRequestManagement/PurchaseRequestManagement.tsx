import Button from '@/components/ui/Button/Button';
import { ColumnConfig, DataGrid } from '@/components/ui/DataGrid/DataGrid';
import Modal from '@/components/ui/Modal/Modal';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';
import Typography from '@/components/ui/Typography';
import { useOrganizationContext } from '@/context/OrganizationContext';
import { useAuth } from '@/hooks/useAuth';
import { usePurchaseRequests, usePurchaseRequestsByUser } from '@/hooks/usePurchaseRequest';
import { getCategoryById, getOrganizationProductById, getProductById } from '@/services/api/catalogService';
import { UserService } from '@/services/api/userService';
import { PurchaseRequest, PurchaseRequestItem } from '@/types/catalog.types';
import { formatDate } from '@/utils/dateUtils';
import { useNavigate } from '@tanstack/react-router';
import { Tab<PERSON>ane<PERSON>, TabView } from 'primereact/tabview';
import React, { useEffect, useRef, useState } from 'react';
import './PurchaseRequestManagement.css';
import { createPurchaseRequestRoute } from '@/routes/private/createPurchaseRequest.route';

const PurchaseRequestManagement: React.FC = () => {
  const navigate = useNavigate();
  const toast = useRef<ToastRef>(null);
  const { organizationId } = useOrganizationContext();
  const { user } = useAuth();

  // State for active tab
  const [activeIndex, setActiveIndex] = useState(0);

  // State for products modal
  const [isProductsModalOpen, setIsProductsModalOpen] = useState(false);
  const [selectedPurchaseRequest, setSelectedPurchaseRequest] = useState<PurchaseRequest | null>(null);

  // State for user name cache
  const [userNameCache, setUserNameCache] = useState<Map<number, string>>(new Map());

  // State for category name cache
  const [categoryNameCache, setCategoryNameCache] = useState<Map<number, string>>(new Map());

  // State for product name cache
  const [productNameCache, setProductNameCache] = useState<Map<string, string>>(new Map());

  // Fetch all purchase requests
  const { data: purchaseRequestsData, isLoading: isPurchaseRequestsLoading } = usePurchaseRequests(
    organizationId || 0,
    { sort: 'requestDate,desc' }
  );
  const purchaseRequests = purchaseRequestsData?.data || [];

  // Fetch user's purchase requests
  const { data: userPurchaseRequestsData, isLoading: isUserPurchaseRequestsLoading } = usePurchaseRequestsByUser(
    organizationId || 0,
    user?.id || 0
  );
  const userPurchaseRequests = userPurchaseRequestsData || [];

  // Helper function to fetch user name
  const fetchUserName = async (userId: number): Promise<string> => {
    if (userNameCache.has(userId)) {
      return userNameCache.get(userId)!;
    }

    try {
      const user = await UserService.getUserById(userId, organizationId!);
      const name = user.name || 'Unknown User';
      setUserNameCache(prev => new Map(prev.set(userId, name)));
      return name;
    } catch (error) {
      const fallbackName = 'Unknown User';
      setUserNameCache(prev => new Map(prev.set(userId, fallbackName)));
      return fallbackName;
    }
  };

  // Helper function to fetch category name
  const fetchCategoryName = async (categoryId: number): Promise<string> => {
    if (categoryNameCache.has(categoryId)) {
      return categoryNameCache.get(categoryId)!;
    }

    try {
      const categoryResponse = await getCategoryById(categoryId);
      const name = categoryResponse.data?.name || 'Unknown Category';
      setCategoryNameCache(prev => new Map(prev.set(categoryId, name)));
      return name;
    } catch (error) {
      const fallbackName = 'Unknown Category';
      setCategoryNameCache(prev => new Map(prev.set(categoryId, fallbackName)));
      return fallbackName;
    }
  };

  // Helper function to fetch product name
  const fetchProductName = async (productId: number, organizationProductId: number | undefined): Promise<string> => {
    const cacheKey = organizationProductId ? `org_${organizationProductId}` : `product_${productId}`;

    if (productNameCache.has(cacheKey)) {
      return productNameCache.get(cacheKey)!;
    }

    try {
      let name = 'Unknown Product';

      if (organizationProductId) {
        const orgProductResponse = await getOrganizationProductById(organizationId!, organizationProductId);
        name = orgProductResponse.data?.name || 'Unknown Organization Product';
      } else if (productId) {
        const productResponse = await getProductById(productId);
        name = productResponse.data?.name || 'Unknown Product';
      }

      setProductNameCache(prev => new Map(prev.set(cacheKey, name)));
      return name;
    } catch (error) {
      const fallbackName = organizationProductId ? 'Unknown Organization Product' : 'Unknown Product';
      setProductNameCache(prev => new Map(prev.set(cacheKey, fallbackName)));
      return fallbackName;
    }
  };

  // Component for displaying user name
  const UserNameCell: React.FC<{ userId: number }> = ({ userId }) => {
    const [name, setName] = useState<string>('Loading...');

    useEffect(() => {
      fetchUserName(userId).then(setName);
    }, [userId]);

    return <div className="user-name-cell">{name}</div>;
  };

  // Component for displaying category name
  const CategoryNameCell: React.FC<{ categoryId: number }> = ({ categoryId }) => {
    const [name, setName] = useState<string>('Loading...');

    useEffect(() => {
      fetchCategoryName(categoryId).then(setName);
    }, [categoryId]);

    return <div className="category-name-cell">{name}</div>;
  };

  // Component for displaying product name
  const ProductNameCell: React.FC<{ item: PurchaseRequestItem }> = ({ item }) => {
    const [name, setName] = useState<string>('Loading...');
    const [productType, setProductType] = useState<string>('');

    useEffect(() => {
      if (item.productId || item.organizationProductId) {
        fetchProductName(item.productId || 0, item.organizationProductId).then(setName);
        setProductType(item.organizationProductId ? 'Organization Product' : 'Global Product');
      } else {
        setName('Unknown Product');
        setProductType('Unknown Type');
      }
    }, [item.productId, item.organizationProductId]);

    return (
      <div className="product-name-cell">
        <div className="font-medium">{name}</div>
        <div className="text-sm text-color-secondary">{productType}</div>
      </div>
    );
  };

  // Handle view products
  const handleViewProducts = (purchaseRequest: PurchaseRequest) => {
    setSelectedPurchaseRequest(purchaseRequest);
    setIsProductsModalOpen(true);
  };

  // Define columns for the purchase requests table
  const columns: ColumnConfig[] = [
    {
      field: 'requestedById',
      header: 'Requested By',
      sortable: true,
      body: (rowData: PurchaseRequest) => (
        <UserNameCell userId={rowData.requestedById} />
      ),
    },
    {
      field: 'requestStatus',
      header: 'Request Status',
      sortable: true,
      body: (rowData: PurchaseRequest) => (
        <span className={`status-badge status-${rowData.requestStatus?.toLowerCase()}`}>
          {rowData.requestStatus || 'PENDING'}
        </span>
      ),
    },
    {
      field: 'requestType',
      header: 'Request Type',
      sortable: true,
    },
    {
      field: 'procurementSource',
      header: 'Procurement Source',
      sortable: true,
    },
    {
      field: 'productCategoryId',
      header: 'Product Category',
      sortable: true,
      body: (rowData: PurchaseRequest) => (
        <CategoryNameCell categoryId={rowData.productCategoryId} />
      ),
    },
    {
      field: 'expectedDeliveryDate',
      header: 'Expected Delivery Date',
      sortable: true,
      body: (rowData: PurchaseRequest) => formatDate(rowData.expectedDeliveryDate),
    },
    {
      field: 'actions',
      header: 'Products',
      body: (rowData: PurchaseRequest) => (
        <Button
          variant="outline"
          icon="pi pi-eye"
          size="small"
          onClick={() => handleViewProducts(rowData)}
        />
      ),
    },
  ];

  // Define columns for the user's purchase requests table (without "Requested By" column)
  const userColumnsConfig: ColumnConfig[] = [
    {
      field: 'requestStatus',
      header: 'Request Status',
      sortable: true,
      body: (rowData: PurchaseRequest) => (
        <span className={`status-badge status-${rowData.requestStatus?.toLowerCase()}`}>
          {rowData.requestStatus || 'PENDING'}
        </span>
      ),
    },
    {
      field: 'requestType',
      header: 'Request Type',
      sortable: true,
    },
    {
      field: 'procurementSource',
      header: 'Procurement Source',
      sortable: true,
    },
    {
      field: 'productCategoryId',
      header: 'Product Category',
      sortable: true,
      body: (rowData: PurchaseRequest) => (
        <CategoryNameCell categoryId={rowData.productCategoryId} />
      ),
    },
    {
      field: 'expectedDeliveryDate',
      header: 'Expected Delivery Date',
      sortable: true,
      body: (rowData: PurchaseRequest) => formatDate(rowData.expectedDeliveryDate),
    },
    {
      field: 'actions',
      header: 'Products',
      body: (rowData: PurchaseRequest) => (
        <Button
          variant="outline"
          icon="pi pi-eye"
          size="small"
          onClick={() => handleViewProducts(rowData)}
        />
      ),
    },
  ];

  return (
    <div className="purchase-request-management p-4">
      <Toast ref={toast} position="top-right" />
      <Typography variant="h5" weight="semibold" className="mb-4">
        Purchase Request Management
      </Typography>
      <div className="flex gap-2 mb-4">
        <Button variant="primary" size="small" onClick={() => navigate({ to: createPurchaseRequestRoute.to })}>
          Add Product
        </Button>
      </div>

      <TabView activeIndex={activeIndex} onTabChange={(e) => setActiveIndex(e.index)}>
        <TabPanel header="All Purchase Requests">
          <div className="tab-content">
            <DataGrid
              value={purchaseRequests}
              columns={columns}
              totalRecords={purchaseRequests.length}
              loading={isPurchaseRequestsLoading}
              rows={20}
              rowsPerPageOptions={[20, 50, 100]}
              showGridLines={true}
              stripedRows={true}
              emptyMessage="No purchase requests found"
            />
          </div>
        </TabPanel>
        
        <TabPanel header="My Purchase Requests">
          <div className="tab-content">
            <DataGrid
              value={userPurchaseRequests}
              columns={userColumnsConfig}
              totalRecords={userPurchaseRequests.length}
              loading={isUserPurchaseRequestsLoading}
              rows={20}
              rowsPerPageOptions={[20, 50, 100]}
              showGridLines={true}
              stripedRows={true}
              emptyMessage="No purchase requests found"
            />
          </div>
        </TabPanel>
        
        <TabPanel header="My Actions">
          <div className="tab-content">
            <Typography variant="body1" className="text-center p-4">
              My Actions - Coming Soon
            </Typography>
          </div>
        </TabPanel>
      </TabView>

      {/* Products Modal */}
      <Modal
        visible={isProductsModalOpen}
        onHide={() => setIsProductsModalOpen(false)}
        header="Purchase Request Products"
        modalProps={{ style: { width: '70vw' } }}
      >
        {selectedPurchaseRequest && (
          <div className="products-modal-content">
            <Typography variant="h6" className="mb-3">
              Products for Request #{selectedPurchaseRequest.id}
            </Typography>

            {selectedPurchaseRequest.purchaseRequestItems && selectedPurchaseRequest.purchaseRequestItems.length > 0 ? (
              <div className="products-list">
                {selectedPurchaseRequest.purchaseRequestItems.map((item: PurchaseRequestItem, index: number) => (
                  <div key={index} className="product-item p-3 mb-2 border-1 border-round">
                    <div className="flex justify-content-between align-items-center">
                      <div className="product-info">
                        <ProductNameCell item={item} />
                        {item.onlineUrl && (
                          <Typography variant="body2" className="text-color-secondary mt-1">
                            URL: {item.onlineUrl}
                          </Typography>
                        )}
                      </div>
                      <div className="quantity-info">
                        <Typography variant="body1" weight="medium">
                          Qty: {item.quantity}
                        </Typography>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <Typography variant="body1" className="text-center p-4">
                No products found for this request
              </Typography>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default PurchaseRequestManagement;
